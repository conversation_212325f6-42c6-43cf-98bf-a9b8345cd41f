// Initialize Material Design Components
mdc.autoInit();

// Loading and Animation Functions
function showPagePreloader() {
    const preloader = document.getElementById('page-preloader');
    preloader.classList.remove('hidden');
}

function hidePagePreloader() {
    const preloader = document.getElementById('page-preloader');
    preloader.classList.add('hidden');

    // Remove preloader from DOM after animation completes
    setTimeout(() => {
        preloader.style.display = 'none';
    }, 500);
}

function showLoadingOverlay(text = 'Processing...') {
    const overlay = document.getElementById('loading-overlay');
    const loadingText = document.getElementById('loading-text');
    loadingText.textContent = text;
    overlay.classList.add('active');
}

function hideLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    overlay.classList.remove('active');
}

// Targeted Loading Functions
function showTargetedLoading(targetSelector, text = 'Loading...') {
    const targetElement = document.querySelector(targetSelector);
    if (!targetElement) return;

    // Find or create loading overlay for this target
    let overlay = targetElement.querySelector('.target-loading-overlay');
    if (!overlay) {
        // Create overlay if it doesn't exist
        overlay = document.createElement('div');
        overlay.className = 'target-loading-overlay';
        overlay.innerHTML = `
            <div class="target-loading-content">
                <div class="simple-spinner">
                    <svg class="spinner-svg" viewBox="0 0 50 50">
                        <circle class="spinner-circle" cx="25" cy="25" r="20" fill="none" stroke="#6200ea" stroke-width="3" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                            <animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                            <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                        </circle>
                    </svg>
                </div>
                <div class="target-loading-text"></div>
            </div>
        `;
        targetElement.appendChild(overlay);
    }

    // Update text and show overlay
    const loadingText = overlay.querySelector('.target-loading-text');
    if (loadingText) {
        loadingText.textContent = text;
    }

    // Ensure target element has relative positioning
    const computedStyle = window.getComputedStyle(targetElement);
    if (computedStyle.position === 'static') {
        targetElement.style.position = 'relative';
    }

    overlay.classList.add('active');
}

function hideTargetedLoading(targetSelector) {
    const targetElement = document.querySelector(targetSelector);
    if (!targetElement) return;

    const overlay = targetElement.querySelector('.target-loading-overlay');
    if (overlay) {
        overlay.classList.remove('active');
    }
}

// Simulate loading delay for better UX (can be removed in production)
function simulateLoading(callback, delay = 300) {
    setTimeout(callback, delay);
}

// Student Data
const studentsData = [
    {
        id: '*********',
        name: 'Alexander Thompson',
        level: 'Grade 10',
        birth: 'March 15, 2008',
        photo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 15'
    },
    {
        id: '*********',
        name: 'Sophia Rodriguez',
        level: 'Grade 11',
        birth: 'July 22, 2007',
        photo: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 14'
    },
    {
        id: 'ST2024003',
        name: 'Marcus Johnson',
        level: 'Grade 9',
        birth: 'November 8, 2009',
        photo: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 14'
    },
    {
        id: 'ST2024004',
        name: 'Emma Williams',
        level: 'Grade 12',
        birth: 'February 3, 2006',
        photo: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 13'
    },
    {
        id: 'ST2024005',
        name: 'Daniel Chen',
        level: 'Grade 10',
        birth: 'September 12, 2008',
        photo: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 13'
    },
    {
        id: 'ST2024006',
        name: 'Isabella Garcia',
        level: 'Grade 11',
        birth: 'May 18, 2007',
        photo: 'https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 12'
    },
    {
        id: 'ST2024007',
        name: 'Ryan Martinez',
        level: 'Grade 9',
        birth: 'December 1, 2009',
        photo: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 12'
    },
    {
        id: 'ST2024008',
        name: 'Olivia Davis',
        level: 'Grade 12',
        birth: 'April 7, 2006',
        photo: 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 11'
    }
];

// Data Table State
let currentPage = 1;
let perPage = 10;
let sortColumn = '';
let sortDirection = '';
let searchQuery = '';
let filteredData = [...studentsData];

// Filter State
let gradeFilter = '';

// MDC Component instances
let searchTextField;
let perPageSelect;
let dataTable;
let gradeFilterSelect;

// Data Table Functions
function filterData() {
    // Show targeted loading for table
    showTargetedLoading('.data-table-container', 'Filtering...');

    simulateLoading(() => {
        filteredData = studentsData.filter(student => {
            // Search filter
            const matchesSearch = !searchQuery ||
                student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                student.level.toLowerCase().includes(searchQuery.toLowerCase()) ||
                student.id.toLowerCase().includes(searchQuery.toLowerCase());

            // Grade filter
            const matchesGrade = !gradeFilter || student.level === gradeFilter;

            return matchesSearch && matchesGrade;
        });

        currentPage = 1;
        renderTable();
        updateHeaderFilterState();

        // Hide targeted loading
        hideTargetedLoading('.data-table-container');
    }, 300);
}

function sortData(column) {
    // Show targeted loading for table
    showTargetedLoading('.data-table-container', 'Sorting...');

    simulateLoading(() => {
        if (sortColumn === column) {
            sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            sortColumn = column;
            sortDirection = 'asc';
        }

        filteredData.sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];

            if (column === 'birth') {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            }

            if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1;
            if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1;
            return 0;
        });

        updateSortHeaders();
        renderTable();

        // Hide targeted loading
        hideTargetedLoading('.data-table-container');
    }, 250);
}

function updateSortHeaders() {
    document.querySelectorAll('.mdc-data-table__header-cell--sortable').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
        const icon = th.querySelector('.sort-icon');
        if (icon) {
            icon.textContent = 'unfold_more';
        }

        if (th.dataset.column === sortColumn) {
            th.classList.add(sortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
            if (icon) {
                icon.textContent = sortDirection === 'asc' ? 'keyboard_arrow_up' : 'keyboard_arrow_down';
            }
        }
    });
}

function renderTable() {
    const tableBody = document.getElementById('table-body');
    const startIndex = (currentPage - 1) * perPage;
    const endIndex = startIndex + perPage;
    const pageData = filteredData.slice(startIndex, endIndex);

    // Clear existing content with fade out effect
    const existingRows = tableBody.querySelectorAll('.mdc-data-table__row');
    existingRows.forEach((row, index) => {
        row.style.animation = `fadeOut 0.2s ease forwards`;
        row.style.animationDelay = `${index * 0.02}s`;
    });

    setTimeout(() => {
        if (pageData.length === 0) {
            tableBody.innerHTML = `
                <tr class="mdc-data-table__row">
                    <td class="mdc-data-table__cell" colspan="7" style="text-align: center; padding: 48px;">
                        <div style="color: #757575;">
                            <span class="material-icons" style="font-size: 48px; margin-bottom: 16px; display: block;">search_off</span>
                            No students found
                        </div>
                    </td>
                </tr>
            `;
        } else {
            tableBody.innerHTML = pageData.map(student => `
                <tr class="mdc-data-table__row table-row" data-student-id="${student.id}">
                    <td class="mdc-data-table__cell">
                        <div class="student-photo" style="background-image: url('${student.photo}')"></div>
                    </td>
                    <th class="mdc-data-table__cell" scope="row">${student.name}</th>
                    <td class="mdc-data-table__cell">${student.level}</td>
                    <td class="mdc-data-table__cell">${student.birth}</td>
                    <td class="mdc-data-table__cell">${student.id}</td>
                    <td class="mdc-data-table__cell">${student.date}</td>
                    <td class="mdc-data-table__cell">
                        <div class="table-actions">
                            <button class="mdc-icon-button" title="View Details">
                                <span class="material-icons">visibility</span>
                            </button>
                            <button class="mdc-icon-button" title="Edit">
                                <span class="material-icons">edit</span>
                            </button>
                            <button class="mdc-icon-button" title="More Options">
                                <span class="material-icons">more_vert</span>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        updatePagination();
        attachTableEventListeners();
    }, existingRows.length > 0 ? 100 : 0);
}

function updatePagination() {
    const totalPages = Math.ceil(filteredData.length / perPage);
    const startItem = filteredData.length === 0 ? 0 : (currentPage - 1) * perPage + 1;
    const endItem = Math.min(currentPage * perPage, filteredData.length);

    // Update pagination info
    document.getElementById('pagination-info').textContent =
        `Showing ${startItem}-${endItem} of ${filteredData.length} students`;

    // Update pagination buttons
    document.getElementById('prev-page').disabled = currentPage === 1;
    document.getElementById('next-page').disabled = currentPage === totalPages || totalPages === 0;

    // Update page numbers
    const pagesContainer = document.getElementById('pagination-pages');
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    pagesContainer.innerHTML = '';
    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.className = `pagination-page ${i === currentPage ? 'active' : ''}`;
        pageBtn.textContent = i;
        pageBtn.addEventListener('click', (e) => {
            if (i !== currentPage) {
                createRipple(e, pageBtn, true);
                showTargetedLoading('.data-table-container', 'Loading...');

                simulateLoading(() => {
                    currentPage = i;
                    renderTable();
                    hideTargetedLoading('.data-table-container');
                }, 200);
            }
        });
        pagesContainer.appendChild(pageBtn);
    }
}

function attachTableEventListeners() {
    // Add ripple effects to table rows
    document.querySelectorAll('.mdc-data-table__row.table-row').forEach(row => {
        row.addEventListener('click', (e) => {
            // Don't trigger if action button was clicked
            if (e.target.closest('.mdc-icon-button')) return;

            createRipple(e, row, true);

            // Extract student data for bottom sheet
            const studentId = row.dataset.studentId;
            const student = studentsData.find(s => s.id === studentId);

            if (student) {
                const studentData = {
                    id: student.id,
                    name: student.name,
                    level: student.level,
                    details: `Born: ${student.birth} • Student ID: ${student.id}`,
                    photo: `url('${student.photo}')`
                };
                showBottomSheet(studentData);
            }
        });
    });

    // Add ripple effects to action buttons
    document.querySelectorAll('.table-actions .mdc-icon-button').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.stopPropagation();
            createRipple(e, btn, true);

            const row = btn.closest('.mdc-data-table__row');
            const studentId = row.dataset.studentId;
            const student = studentsData.find(s => s.id === studentId);
            const action = btn.title.toLowerCase();

            if (action.includes('more')) {
                // Show bottom sheet for more options
                const studentData = {
                    id: student.id,
                    name: student.name,
                    level: student.level,
                    details: `Born: ${student.birth} • Student ID: ${student.id}`,
                    photo: `url('${student.photo}')`
                };
                showBottomSheet(studentData);
            } else {
                console.log(`${action} student:`, student.name);
            }
        });
    });
}

// Header Filter Functions
function updateHeaderFilterState() {
    // Visual feedback can be added here if needed
    console.log('Grade filter applied:', gradeFilter);
}

function initializeDataTableControls() {
    // Initialize MDC Text Field for search
    const searchTextFieldEl = document.querySelector('.table-controls .mdc-text-field');
    if (searchTextFieldEl && window.mdc && window.mdc.textField) {
        searchTextField = new mdc.textField.MDCTextField(searchTextFieldEl);

        // Add search input listener
        const searchInput = document.getElementById('table-search');
        searchInput.addEventListener('input', (e) => {
            searchQuery = e.target.value;
            filterData();
        });
    }

    // Initialize MDC Select for per-page
    const perPageSelectEl = document.querySelector('.per-page-container .mdc-select');
    if (perPageSelectEl && window.mdc && window.mdc.select) {
        perPageSelect = new mdc.select.MDCSelect(perPageSelectEl);

        // Add change listener
        perPageSelect.listen('MDCSelect:change', () => {
            showTargetedLoading('.data-table-container', 'Loading...');

            simulateLoading(() => {
                perPage = parseInt(perPageSelect.value);
                currentPage = 1;
                renderTable();
                hideTargetedLoading('.data-table-container');
            }, 250);
        });
    }

    // Initialize grade filter select
    const gradeFilterEl = document.getElementById('grade-filter');
    if (gradeFilterEl && window.mdc && window.mdc.select) {
        gradeFilterSelect = new mdc.select.MDCSelect(gradeFilterEl);

        // Add change listener for grade filter
        gradeFilterSelect.listen('MDCSelect:change', () => {
            gradeFilter = gradeFilterSelect.value;
            filterData();
        });
    }
}

// Event Listeners
document.addEventListener('DOMContentLoaded', () => {
    // Show page preloader initially
    showPagePreloader();

    // Simulate page loading time
    simulateLoading(() => {
        // Initialize MDC Data Table component
        const dataTableEl = document.querySelector('.mdc-data-table');
        if (dataTableEl && window.mdc && window.mdc.dataTable) {
            dataTable = new mdc.dataTable.MDCDataTable(dataTableEl);
        }

        // Initialize MDC Floating Action Button
        const fabEl = document.getElementById('add-student-fab');
        if (fabEl && window.mdc && window.mdc.ripple) {
            const fabRipple = new mdc.ripple.MDCRipple(fabEl);
        }

        // Initialize controls
        initializeDataTableControls();

        // Add sort listeners
        document.querySelectorAll('.mdc-data-table__header-cell--sortable').forEach(th => {
            th.addEventListener('click', (e) => {
                createRipple(e, th, true);
                sortData(th.dataset.column);
            });
        });

        // Add pagination listeners
        document.getElementById('prev-page').addEventListener('click', (e) => {
            createRipple(e, e.target, true);
            if (currentPage > 1) {
                showTargetedLoading('.data-table-container', 'Loading...');

                simulateLoading(() => {
                    currentPage--;
                    renderTable();
                    hideTargetedLoading('.data-table-container');
                }, 200);
            }
        });

        document.getElementById('next-page').addEventListener('click', (e) => {
            createRipple(e, e.target, true);
            const totalPages = Math.ceil(filteredData.length / perPage);
            if (currentPage < totalPages) {
                showTargetedLoading('.data-table-container', 'Loading...');

                simulateLoading(() => {
                    currentPage++;
                    renderTable();
                    hideTargetedLoading('.data-table-container');
                }, 200);
            }
        });

        // Add event listeners for content header icons
        document.querySelector('.content-header .actions .material-icons[title="Export to Excel"]').addEventListener('click', (e) => {
            createRipple(e, e.target, true);
            console.log('Export to Excel clicked');
            // TODO: Implement Excel export functionality
        });

        document.querySelector('.content-header .actions .material-icons[title="Import Data"]').addEventListener('click', (e) => {
            createRipple(e, e.target, true);
            console.log('Import Data clicked');
            // TODO: Implement data import functionality
        });

        document.querySelector('.content-header .actions .material-icons[title="Toggle View"]').addEventListener('click', (e) => {
            createRipple(e, e.target, true);
            console.log('Toggle View clicked');
            // TODO: Implement view toggle functionality (list/grid)
        });

        // Add event listener for floating action button
        document.getElementById('add-student-fab').addEventListener('click', (e) => {
            console.log('Add Student clicked');
            // TODO: Implement add student functionality
        });

        // Initial render
        renderTable();

        // Hide page preloader after everything is loaded
        hidePagePreloader();
    }, 800); // Slightly longer delay to show the preloader
});

// Ripple Effect Function
function createRipple(event, element, isDark = false) {
    const circle = document.createElement('span');
    const diameter = Math.max(element.clientWidth, element.clientHeight);
    const radius = diameter / 2;
    
    const rect = element.getBoundingClientRect();
    circle.style.width = circle.style.height = `${diameter}px`;
    circle.style.left = `${event.clientX - rect.left - radius}px`;
    circle.style.top = `${event.clientY - rect.top - radius}px`;
    circle.classList.add('ripple');
    
    if (isDark) {
        circle.classList.add('ripple-dark');
    }
    
    const ripple = element.getElementsByClassName('ripple')[0];
    if (ripple) {
        ripple.remove();
    }
    
    element.appendChild(circle);
    
    // Remove ripple after animation
    setTimeout(() => {
        circle.remove();
    }, 600);
}

// Sidebar toggle (works for both mobile and desktop)
const menuBtn = document.getElementById('menu-btn');
const sidebar = document.getElementById('sidebar');
const mainContent = document.querySelector('.main-content');

menuBtn.addEventListener('click', (e) => {
    createRipple(e, menuBtn);

    if (window.innerWidth <= 768) {
        // Mobile behavior: toggle open class
        sidebar.classList.toggle('open');
    } else {
        // Desktop behavior: toggle collapsed class
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('expanded');
    }
});

// Close sidebar when clicking outside on mobile
document.addEventListener('click', (e) => {
    if (window.innerWidth <= 768) {
        if (!sidebar.contains(e.target) && !menuBtn.contains(e.target)) {
            sidebar.classList.remove('open');
        }
    }
});

// Grades submenu toggle
const gradesMenu = document.getElementById('grades-menu');
const gradesSubmenu = document.getElementById('grades-submenu');

gradesMenu.addEventListener('click', (e) => {
    createRipple(e, gradesMenu, true);
    gradesMenu.classList.toggle('expanded');
    gradesSubmenu.classList.toggle('expanded');
});

// Sidebar item interactions
document.querySelectorAll('.sidebar-item:not(#grades-menu)').forEach(item => {
    item.addEventListener('click', (e) => {
        createRipple(e, item, true);
        // Remove active class from all items
        document.querySelectorAll('.sidebar-item').forEach(i => i.classList.remove('active'));
        document.querySelectorAll('.submenu-item').forEach(i => i.classList.remove('active'));
        // Add active class to clicked item
        item.classList.add('active');
    });
});

// Submenu item interactions
document.querySelectorAll('.submenu-item').forEach(item => {
    item.addEventListener('click', (e) => {
        e.stopPropagation();
        createRipple(e, item, true);
        // Remove active class from all items
        document.querySelectorAll('.sidebar-item').forEach(i => i.classList.remove('active'));
        document.querySelectorAll('.submenu-item').forEach(i => i.classList.remove('active'));
        // Add active class to clicked submenu item
        item.classList.add('active');
    });
});

// Edit button interactions
document.querySelectorAll('.edit-btn').forEach(btn => {
    btn.addEventListener('click', (e) => {
        e.stopPropagation();
        createRipple(e, btn, true);
        const studentName = btn.closest('.student-item').querySelector('.student-name').textContent;
        console.log('Edit student:', studentName);
        // Here you would typically open an edit modal or navigate to edit page
    });
});

// Bottom Sheet Elements
const bottomSheetOverlay = document.getElementById('bottom-sheet-overlay');
const bottomSheet = document.getElementById('bottom-sheet');
const bottomSheetClose = document.getElementById('bottom-sheet-close');
const previewPhoto = document.getElementById('preview-photo');
const previewName = document.getElementById('preview-name');
const previewDetails = document.getElementById('preview-details');

// Bottom Sheet Functions
function showBottomSheet(studentData) {
    // Populate student preview
    previewPhoto.style.backgroundImage = studentData.photo;
    previewName.textContent = studentData.name;
    previewDetails.innerHTML = `<span class="student-level">${studentData.level}</span>${studentData.details}`;

    // Show bottom sheet
    bottomSheetOverlay.classList.add('active');
    bottomSheet.classList.add('active');

    // Store current student data for actions
    bottomSheet.dataset.studentId = studentData.id;
    bottomSheet.dataset.studentName = studentData.name;
}

function hideBottomSheet() {
    bottomSheetOverlay.classList.remove('active');
    bottomSheet.classList.remove('active');
}

// Bottom Sheet Event Listeners
bottomSheetClose.addEventListener('click', (e) => {
    createRipple(e, bottomSheetClose, true);
    hideBottomSheet();
});

bottomSheetOverlay.addEventListener('click', (e) => {
    if (e.target === bottomSheetOverlay) {
        hideBottomSheet();
    }
});

// Student item interactions
document.querySelectorAll('.student-item').forEach(item => {
    item.addEventListener('click', (e) => {
        // Don't trigger if edit button was clicked
        if (e.target.closest('.edit-btn')) return;

        createRipple(e, item, true);

        // Extract student data
        const studentData = {
            id: item.dataset.studentId || item.querySelector('.student-name').textContent.replace(/\s+/g, '').toLowerCase(),
            name: item.querySelector('.student-name').textContent,
            level: item.querySelector('.student-level').textContent,
            details: item.querySelector('.student-details').textContent.replace(item.querySelector('.student-level').textContent, '').trim(),
            photo: item.querySelector('.student-photo').style.backgroundImage
        };

        showBottomSheet(studentData);
    });
});

// Add ripple effects to app bar action buttons
document.querySelectorAll('.app-bar .actions .material-icons').forEach(icon => {
    icon.addEventListener('click', (e) => {
        createRipple(e, icon);
    });
});

// Add ripple effects to content header icons
document.querySelectorAll('.content-header .material-icons').forEach(icon => {
    icon.addEventListener('click', (e) => {
        createRipple(e, icon, true);
    });
});

// Bottom Sheet Action Handlers
document.getElementById('action-details').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    const studentName = bottomSheet.dataset.studentName;
    console.log('View details for:', studentName);
    hideBottomSheet();
    // Here you would navigate to student details page
});

document.getElementById('action-edit').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    const studentName = bottomSheet.dataset.studentName;
    console.log('Edit student:', studentName);
    hideBottomSheet();
    // Here you would open edit modal or navigate to edit page
});

document.getElementById('action-payment').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    const studentName = bottomSheet.dataset.studentName;
    console.log('Add payment record for:', studentName);
    hideBottomSheet();
    // Here you would open payment modal or navigate to payment page
});

document.getElementById('action-delete').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    const studentName = bottomSheet.dataset.studentName;
    const studentId = bottomSheet.dataset.studentId;

    // Show confirmation dialog
    if (confirm(`Are you sure you want to delete ${studentName}? This action cannot be undone.`)) {
        console.log('Delete student:', studentName, 'ID:', studentId);
        hideBottomSheet();
        // Here you would call delete API and remove from DOM
        // Example: removeStudentFromList(studentId);
    }
});

// Add ripple effects to bottom sheet actions
document.querySelectorAll('.bottom-sheet-action').forEach(action => {
    action.addEventListener('click', (e) => {
        if (!e.currentTarget.id) { // Only add ripple if not handled by specific action
            createRipple(e, action, true);
        }
    });
});

// Keyboard support for bottom sheet
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && bottomSheetOverlay.classList.contains('active')) {
        hideBottomSheet();
    }
});

// Responsive sidebar handling
window.addEventListener('resize', () => {
    if (window.innerWidth > 768) {
        // Desktop: remove mobile classes
        sidebar.classList.remove('open');
    } else {
        // Mobile: remove desktop classes and reset to mobile behavior
        sidebar.classList.remove('collapsed');
        mainContent.classList.remove('expanded');
    }
});
